import requests
from bs4 import BeautifulSoup
import pandas as pd
import sqlite3

banks_list = "https://en.wikipedia.org/wiki/List_of_largest_banks"
exchange_rate = "exchange_rate.csv"
table_attrs_extraction = ["Name", "MC_USD_Billion"]
table_attrs_final = ["Name", "MC_USD_Billion", "MC_GBP_Billion", "MC_EUR_Billion", "MC_INR_Billion"]
csv_path = "./Largest_banks_data.csv"
db_name = "Banks"
table_name = "Largest_banks"
log_file = "code_log.txt"

banks_html_page = requests.get(banks_list, headers={"User-Agent": "Mozilla/5.0"}).text
bf_page = BeautifulSoup(banks_html_page, "html.parser")
exchange_pd_table = pd.read_csv(exchange_rate)


def find_banks_table(bf_page):
    print("Checking tables...")
    get_all_tables = bf_page.find_all('table')
    needed_table = ''
    print(len(get_all_tables))
    for table in get_all_tables:
        if table.find_all("th")[1].text.startswith("Bank name"):
            print("Table found")
            needed_table = table
            break
        else:
            print("Issues with finding a table")
    return needed_table


banks_table = find_banks_table(bf_page)


def extract_data(banks_table, table_attrs_extraction):
    print("Extracting data...")
    df = pd.DataFrame(columns=table_attrs_extraction)
    for row in banks_table.find_all("tr")[1:]:
        col = row.find_all("td")
        if len(col) != 0:
            data_dict = {"Name": col[1].text.strip(), "MC_USD_Billion": col[2].text.strip()}
            df1 = pd.DataFrame(data_dict, index=[0])
            df = pd.concat([df, df1], ignore_index=True)
    return df


df = extract_data(banks_table, table_attrs_extraction)


def transform_data(df, exchange_pd_table):
    print("Transforming data...")
    exchange_rate_dict = exchange_pd_table.set_index("Currency").to_dict()["Rate"]
    print(exchange_rate_dict)
    for currency in exchange_rate_dict:
        print(f"Transforming to {currency}", exchange_rate_dict[currency])
        df[f"MC_{currency}_Billion"] = (
                pd.to_numeric(df["MC_USD_Billion"], errors='coerce') * float(exchange_rate_dict[currency])).round(2)
    return df


df = transform_data(df, exchange_pd_table)
print(df)
