import requests
from bs4 import BeautifulSoup
import pandas as pd
import sqlite3

banks_list = "https://en.wikipedia.org/wiki/List_of_largest_banks"
exchange_rate_url = "https://cf-courses-data.s3.us.cloud-object-storage.appdomain.cloud/IBMSkillsNetwork-PY0221EN-Coursera/labs/v2/exchange_rate.csv"
table_attrs_extraction = ["Name", "MC_USD_Billion"]
table_attrs_final = ["Name", "MC_USD_Billion", "MC_GBP_Billion", "MC_EUR_Billion", "MC_INR_Billion"]
csv_path = "./Largest_banks_data.csv"
db_name = "Banks"
table_name = "Largest_banks"
log_file = "code_log.txt"

banks_html_page = requests.get(banks_list, headers=[("User-Agent", "Mozilla/5.0")]).text
print(banks_html_page)
bf_page = BeautifulSoup(banks_html_page, "html.parser")


def find_banks_table(bf_page):
    print("Checking tables...")
    get_all_tables = bf_page.find_all('table')
    needed_table = ''
    print(len(get_all_tables))
    for table in get_all_tables:
        if table.find_all("th")[1].text == "Bank name":
            print("Table found")
            print(table)
            needed_table = table
            break
        else:
            print("Issues with finding a table")
    return needed_table


find_banks_table(bf_page)
