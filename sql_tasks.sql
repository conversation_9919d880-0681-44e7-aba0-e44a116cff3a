/*
Basic SQL Commands */
SELECT: The most common command, used to retrieve data from a database.

SELECT column1, column2 FROM table_name;

SELECT * FROM table_name; (Selects all columns)

-- FROM: Specifies the table from which to retrieve the data.

WHERE: Filters records based on a specified condition.

SELECT column1 FROM table_name WHERE condition;

/*GROUP BY: Groups rows that have the same values in specified columns into summary rows. This is typically used with aggregate functions.*/

SELECT column1, COUNT(column2) FROM table_name GROUP BY column1;

/*ORDER BY: Sorts the result set in ascending (ASC) or descending (DESC) order.*/

SELECT * FROM table_name ORDER BY column1 ASC;

-- LIMIT: Restricts the number of rows returned by the query.

SELECT * FROM table_name LIMIT 10;

/*DISTINCT: Returns only unique values in the specified column. */

SELECT DISTINCT column1 FROM table_name;

/*Data Manipulation Language (DML)
These commands are used to modify data within tables. */

INSERT INTO: Adds new records to a table.

INSERT INTO table_name (column1, column2) VALUES (value1, value2);

/*UPDATE: Modifies existing records in a table.*/

UPDATE table_name SET column1 = value1 WHERE condition;

/*DELETE FROM: Deletes existing records from a table.*/

DELETE FROM table_name WHERE condition;

/*Aggregate Functions
These functions perform a calculation on a set of values and return a single value. */

-- COUNT(): Counts the number of rows.

SELECT COUNT(column_name) FROM table_name;

-- SUM(): Calculates the sum of a set of values.

SELECT SUM(column_name) FROM table_name;

-- AVG(): Calculates the average of a set of values.

SELECT AVG(column_name) FROM table_name;

-- MIN(): Returns the minimum value.

SELECT MIN(column_name) FROM table_name;

-- MAX(): Returns the maximum value.

SELECT MAX(column_name) FROM table_name;

/*SQL Joins */
Joins are used to combine rows from two or more tables based on a related column between them.

-- INNER JOIN: Returns records that have matching values in both tables.

SELECT * FROM table1 INNER JOIN table2 ON table1.column_name = table2.column_name;

-- LEFT (OUTER) JOIN: Returns all records from the left table and the matched records from the right table.

SELECT * FROM table1 LEFT JOIN table2 ON table1.column_name = table2.column_name;

-- RIGHT (OUTER) JOIN: Returns all records from the right table and the matched records from the left table.

SELECT * FROM table1 RIGHT JOIN table2 ON table1.column_name = table2.column_name;

-- FULL (OUTER) JOIN: Returns all records when there is a match in either the left or the right table.

SELECT * FROM table1 FULL JOIN table2 ON table1.column_name = table2.column_name;

/*Window Functions
Window functions perform calculations across a set of table rows that are somehow related to the current row. Unlike aggregate functions, they don't group rows into a single output row. */

OVER(): Defines the "window" or set of rows for the function to operate on.

-- PARTITION BY: Divides the data into partitions. The function is applied to each partition independently.

SELECT column1, SUM(column2) OVER (PARTITION BY column3) FROM table_name;

ORDER BY: Orders rows within each partition. This is crucial for ranking functions.

-- ROW_NUMBER(): Assigns a unique, sequential integer to each row within a partition.

SELECT column1, ROW_NUMBER() OVER (PARTITION BY column2 ORDER BY column3) FROM table_name;

-- RANK(): Assigns a rank to each row within a partition, with identical values receiving the same rank.

DENSE_RANK(): Similar to RANK(), but without gaps in the ranking sequence.

/*Common Table Expressions (CTEs)
CTEs are temporary, named result sets created within a SELECT statement. They improve query readability and can be referenced multiple times within the same query.

WITH: The keyword used to define a CTE. */

WITH cte_name AS (SELECT * FROM table_name) SELECT * FROM cte_name;

SELECT name,
       salary,
       CASE
         WHEN salary > 8000 THEN 'High'
         WHEN salary BETWEEN 5000 AND 8000 THEN 'Medium'
         ELSE 'Low'
       END AS salary_level
FROM employees;


/*Write a solution to report the first name, last name, city, and state of each person in the Person table. If the address of a personId is not present in the Address table, report null instead.*/
SELECT p.firstName, p.lastName, a.city, a.state
FROM Person as p
OUTER JOIN Address AS a
on p.personId = a.personId

--  Write a solution to find the second highest distinct salary from the Employee table. If there is no second highest salary, return null (return None in Pandas).

SELECT MAX(salary) AS SecondHighestSalary
FROM Employee
WHERE salary < (SELECT MAX(salary) FROM Employee)

-- Write a solution to find the nth highest distinct salary from the Employee table. If there are less than n distinct salaries, return null.

CREATE FUNCTION getNthHighestSalary(N INT) RETURNS INT
BEGIN
  RETURN (
    SELECT DISTINCT salary as getNthHighestSalary
      FROM Employee
      ORDER BY salary DESC
      LIMIT 1 OFFSET N - 1
  );
END
