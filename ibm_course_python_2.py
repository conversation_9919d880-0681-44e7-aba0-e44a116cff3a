import sqlite3
import pandas as pd


conn = sqlite3.connect('STAFF.db')
table_name = 'INSTRUCTOR'
dep_table = 'Departments'
attribute_list = ['ID', 'FNAME', 'LNAME', 'CITY', 'CCODE']
file_path = 'INSTRUCTOR.csv'
df = pd.read_csv(file_path, names=attribute_list)
df.to_sql(table_name, conn, if_exists='replace', index=False)
print('Table created successfully')
query_statement = f"SELECT * FROM {table_name}"
query_output = pd.read_sql(query_statement, conn)
# print(query_output)
departments_file_path = 'Departments.csv'
departments_df = pd.read_csv(departments_file_path, names=['DEPT_ID', 'DEP_NAME', 'MANAGER_ID', 'LOC_ID'])
departments_df.to_sql(dep_table, conn, if_exists='append', index=False)
data_to_add = {'DEPT_ID':[9],'DEP_NAME':[None], 'MANAGER_ID':[1], 'LOC_ID':['10']}
df_to_add = pd.DataFrame(data_to_add)
df_to_add.to_sql(dep_table, conn, if_exists='append', index=False)
query_statement = f"SELECT * FROM {table_name} join {dep_table} on {table_name}.ID = {dep_table}.DEPT_ID"
print(pd.read_sql(query_statement, conn))
