# You have to complete the following tasks for this project
#
# Write a data extraction function to retrieve the relevant information from the required URL.
#
# Transform the available GDP information into 'Billion USD' from 'Million USD'.
#
# Load the transformed information to the required CSV file and as a database file.
#
# Run the required query on the database.
#
# Log the progress of the code with appropriate timestamp
import pandas as pd
import requests
from bs4 import BeautifulSoup as bs
import sqlite3

url = 'https://web.archive.org/web/20230902185326/https://en.wikipedia.org/wiki/List_of_countries_by_GDP_%28nominal%29'
table_name = "Top_50"
count = 0
table_attributes = ['Country', 'GDP_USD_Millions']


def extract_data(url, table_attributes):
    df = pd.DataFrame(columns=table_attributes)
    html_page = requests.get(url).text
    page_data = bs(html_page, 'html.parser')
    gdp_table = page_data.find_all('table', {'class': 'wikitable'})
    gdp_table_rows = gdp_table[0].find('tbody').find_all('tr')
    for row in gdp_table_rows[1:]:
        row_values = row.find_all('td')
        if len(row_values) != 0:
            if row_values[0].find('a') is not None and '-' not in row_values[2]:
                # country =
                # gdp =
                # print(gdp)
                dict_data = {'Country': row_values[0].a.contents[0], 'GDP_USD_Millions': row.values[2].text}
                df1 = pd.DataFrame(dict_data, index=[0])
                df = pd.concat([df, df1], ignore_index=True)

    print('df:', df)
    return df


def transform_data(df):
    GDP_list = df['GDP_USD_Millions'].tolist()
    GDP_list = [float("".join(x.split(','))) for x in GDP_list]


def load_to_csv(df, csv_path):
    pass


def load_to_db(df, db_name, table_name):
    pass


def query_db(db_name, table_name):
    pass


def log_progress(message):
    pass


extract_data(url, table_attributes)
