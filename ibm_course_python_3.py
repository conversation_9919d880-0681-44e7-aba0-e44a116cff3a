# You have to complete the following tasks for this project
#
# Write a data extraction function to retrieve the relevant information from the required URL.
#
# Transform the available GDP information into 'Billion USD' from 'Million USD'.
#
# Load the transformed information to the required CSV file and as a database file.
#
# Run the required query on the database.
#
# Log the progress of the code with appropriate timestamp
import pandas as pd
import requests
from bs4 import BeautifulSoup as bs
import sqlite3

url = 'https://web.archive.org/web/20230902185326/https://en.wikipedia.org/wiki/List_of_countries_by_GDP_%28nominal%29'
table_name = "Top_50"
count = 0

df = pd.DataFrame(columns=['Country', 'GDP (Billion USD)'])
html_page = requests.get(url).text
page_data = bs(html_page, 'html.parser')
gdp_table = page_data.find_all('table', {'class': 'wikitable'})
gdp_table_rows = gdp_table[0].find_all('tr')
for row in gdp_table_rows[1:]:
    if count == 5:
        break
    row_values = row.find_all('td')
    print(row.values)
    count += 1
