# You have to complete the following tasks for this project
#
# Write a data extraction function to retrieve the relevant information from the required URL.
#
# Transform the available GDP information into 'Billion USD' from 'Million USD'.
#
# Load the transformed information to the required CSV file and as a database file.
#
# Run the required query on the database.
#
# Log the progress of the code with appropriate timestamp
import pandas as pd
import requests
from bs4 import BeautifulSoup as bs
import sqlite3

url = 'https://web.archive.org/web/20230902185326/https://en.wikipedia.org/wiki/List_of_countries_by_GDP_%28nominal%29'
table_name = "top_gdp_countries"
table_attributes = ['Country', 'GDP_USD_Millions']
sql_columns = ['Country', 'GDP_USD_Billions']
csv_path = './top_gdp_countries.csv'
conn = sqlite3.connect(table_name)
query_statement = f"SELECT {sql_columns[0]} FROM {table_name} WHERE {sql_columns[1]} >= 100"


def extract_data(url, table_attributes):
    df = pd.DataFrame(columns=table_attributes)
    html_page = requests.get(url).text
    page_data = bs(html_page, 'html.parser')
    gdp_table = page_data.find_all('table', {'class': 'wikitable'})
    # print(gdp_table)
    gdp_table_rows = gdp_table[0].find('tbody').find_all('tr')
    for row in gdp_table_rows[1:]:
        row_values = row.find_all('td')
        if len(row_values) >= 3:
            if row_values[0].find('a') is not None and row_values[2] is not None and len(row_values[2].text) > 1:
                country = row_values[0].a.contents[0]
                gdp = row_values[2].text
                dict_data = {'Country': country, 'GDP_USD_Millions': gdp}
                df1 = pd.DataFrame(dict_data, index=[0])
                df = pd.concat([df, df1], ignore_index=True)

    return df


def transform_data(df):
    df['GDP_USD_Millions'] = (pd.to_numeric(df['GDP_USD_Millions'].str.replace(',', ''), errors='coerce') / 1000).round(
        2)
    df = df.rename(columns={"GDP_USD_Millions": "GDP_USD_Billions"})
    # print('transformed df', df_final)
    return df


def load_to_csv(df, csv_path):
    df.to_csv(csv_path)


def load_to_db(df, sql_connection, table_name):
    df.to_sql(table_name, sql_connection, if_exists='replace', index=False)


def query_db(query_statement, connection):
    query_output = pd.read_sql(query_statement, connection)
    print(query_output)


def log_progress(message):
    pass


df = extract_data(url, table_attributes)
df = transform_data(df)
load_to_csv(df, csv_path)
load_to_db(df, conn, table_name)
query_db(query_statement, conn)
conn.close()
