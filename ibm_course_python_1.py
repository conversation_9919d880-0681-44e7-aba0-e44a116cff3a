import requests
import sqlite3
import pandas as pd
from bs4 import BeautifulSoup

# Scenario
# Consider that you have been hired by a Multiplex management organization
# to extract the information of the top 50 movies with the best average rating from the web link shared below.
# The information required is Average Rank, Film, and Year.
# You are required to write a Python script webscraping_movies.py that extracts the information and saves it to a CSV file top_50_films.csv. You are also required to save the same information to a database Movies.db under the table name Top_50.

url = "https://web.archive.org/web/20230902185655/https://en.everybodywiki.com/100_Most_Highly-Ranked_Films"
db_name = "Movies.db"
table_name  = "Top_50"
csv_path = "./"
df = pd.DataFrame(columns=["Average Rank", "Film", "Year"])
count = 0

conn = sqlite3.connect(db_name)
html_page = requests.get(url).text
data = BeautifulSoup(html_page, "html.parser")
tables = data.find_all("tbody")
rows = tables[0].find_all("tr")

for row in rows:
    if count < 50:
        col = row.find_all("td")
        if len(col)!=0:
            data_dict = {"Average Rank": col[0].text, "Film": col[1].text, "Year": col[2].text}
            print(data_dict)
            df1 = pd.DataFrame(data_dict,index=[0])
            df = pd.concat([df,df1],ignore_index=True)
            count += 1
    else:
        break



df.to_csv(csv_path+"top_50_films.csv")
df.to_sql(table_name, conn, if_exists="replace",index=False)
conn.close()